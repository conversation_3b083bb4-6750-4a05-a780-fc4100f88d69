{"name": "cli-width", "version": "3.0.0", "description": "Get stdout window width, with two fallbacks, tty and then a default.", "main": "index.js", "scripts": {"test": "node test | tspec", "coverage": "nyc node test | tspec", "coveralls": "npm run coverage -s && coveralls < coverage/lcov.info", "release": "standard-version"}, "repository": {"type": "git", "url": "**************:knownasilya/cli-width.git"}, "author": "<PERSON><PERSON> <known<PERSON><PERSON>@gmail.com>", "license": "ISC", "bugs": {"url": "https://github.com/knownasilya/cli-width/issues"}, "homepage": "https://github.com/knownasilya/cli-width", "engines": {"node": ">= 10"}, "devDependencies": {"coveralls": "^3.0.11", "nyc": "^15.0.1", "standard-version": "^7.1.0", "tap-spec": "^5.0.0", "tape": "^4.13.2"}}